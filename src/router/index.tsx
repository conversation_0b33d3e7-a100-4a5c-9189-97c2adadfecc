import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/LoginView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    name: 'Dashboard',
    component: () => import('@/views/DashboardLayout.vue'),
    meta: { requiresAuth: true },
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'DashboardHome',
        component: () => import('@/views/dashboard/DashboardHome.vue')
      },
      {
        path: 'users',
        name: 'UserManagement',
        component: () => import('@/views/users/UserManagement.vue')
      },
      {
        path: 'products',
        name: 'ProductManagement',
        component: () => import('@/views/products/ProductManagement.vue')
      },
      {
        path: 'products/add',
        name: 'AddProduct',
        component: () => import('@/views/products/AddProduct.vue')
      },
      {
        path: 'products/edit/:id',
        name: 'EditProduct',
        component: () => import('@/views/products/EditProduct.vue')
      },
      {
        path: 'orders',
        name: 'OrderManagement',
        component: () => import('@/views/orders/OrderManagement.vue')
      },
      {
        path: 'orders/:id',
        name: 'OrderDetails',
        component: () => import('@/views/orders/OrderDetails.vue')
      },
      {
        path: 'orders/:id/logistics',
        name: 'AddLogistics',
        component: () => import('@/views/orders/AddLogistics.vue')
      },
      {
        path: 'logistics',
        name: 'LogisticsManagement',
        component: () => import('@/views/logistics/LogisticsManagement.vue')
      },
      {
        path: 'logistics/:id/remarks',
        name: 'AddRemarks',
        component: () => import('@/views/logistics/AddRemarks.vue')
      },
      {
        path: 'logistics/:id/details',
        name: 'LogisticsDetails',
        component: () => import('@/views/logistics/LogisticsDetails.vue')
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Navigation guard for authentication
router.beforeEach((to, from, next) => {
  const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true'

  if (to.meta.requiresAuth && !isAuthenticated) {
    next('/login')
  } else if (to.path === '/login' && isAuthenticated) {
    next('/')
  } else {
    next()
  }
})

export default router