<script setup lang="ts">
import { Menu as IconMenu, Setting } from '@element-plus/icons-vue'
</script>

<template>
  <el-container style="height: 100vh">
    <el-aside width="200px">
      <el-menu router default-active="/">
        <el-menu-item index="/">
          <el-icon><icon-menu /></el-icon>
          <span>Dashboard</span>
        </el-menu-item>
      </el-menu>
    </el-aside>

    <el-container>
      <el-header style="text-align:right">
        <el-button link>
          <el-icon size="20"><Setting /></el-icon>
        </el-button>
      </el-header>

      <el-main>
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<style>
#app { font-family: Avenir, Helvetica, Arial, sans-serif; }
</style>