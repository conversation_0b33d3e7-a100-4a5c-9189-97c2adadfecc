<template>
  <div class="product-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>产品管理</span>
          <el-button type="primary" @click="$router.push('/products/add')">
            <el-icon><Plus /></el-icon>
            添加新产品
          </el-button>
        </div>
      </template>

      <el-table
        :data="productsStore.products"
        style="width: 100%"
        v-loading="productsStore.loading"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="产品名称" width="150" />
        <el-table-column prop="description" label="描述" min-width="200" />
        <el-table-column prop="unitPrice" label="单价" width="100">
          <template #default="scope">
            <span v-if="scope.row.unitPrice > 0">¥{{ scope.row.unitPrice }}</span>
            <span v-else class="text-gray">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="pointsRequired" label="所需积分" width="100">
          <template #default="scope">
            <span v-if="scope.row.pointsRequired > 0">{{ scope.row.pointsRequired }}</span>
            <span v-else class="text-gray">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="stockQuantity" label="库存" width="80" />
        <el-table-column prop="paymentTypes" label="支付方式" width="150">
          <template #default="scope">
            <div class="payment-types">
              <el-tag
                v-for="type in scope.row.paymentTypes"
                :key="type"
                size="small"
                :type="getPaymentTypeColor(type)"
                style="margin-right: 4px;"
              >
                {{ getPaymentTypeText(type) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="120" />
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button
              size="small"
              @click="$router.push(`/products/edit/${scope.row.id}`)"
            >
              修改
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue'
import { useProductsStore } from '@/stores'

const productsStore = useProductsStore()

const getPaymentTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    points: 'success',
    cash: 'warning',
    points_cash: 'info'
  }
  return colorMap[type] || 'info'
}

const getPaymentTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    points: '积分',
    cash: '现金',
    points_cash: '积分+现金'
  }
  return textMap[type] || type
}
</script>

<style scoped>
.product-management {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 18px;
  font-weight: 600;
}

.payment-types {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.text-gray {
  color: #999;
}
</style>
