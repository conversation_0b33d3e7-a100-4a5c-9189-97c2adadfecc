<template>
  <div class="edit-product">
    <el-card v-if="product">
      <template #header>
        <div class="card-header">
          <span>编辑产品</span>
          <el-button @click="$router.back()">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
        </div>
      </template>

      <el-form
        ref="productFormRef"
        :model="productForm"
        :rules="productRules"
        label-width="120px"
        style="max-width: 600px;"
      >
        <el-form-item label="产品名称" prop="name">
          <el-input v-model="productForm.name" placeholder="请输入产品名称" />
        </el-form-item>

        <el-form-item label="产品描述" prop="description">
          <el-input
            v-model="productForm.description"
            type="textarea"
            placeholder="请输入产品描述"
            :rows="4"
          />
        </el-form-item>

        <el-form-item label="产品图片" prop="photo">
          <el-input v-model="productForm.photo" placeholder="请输入图片URL" />
          <div class="form-tip">请输入图片的URL地址</div>
        </el-form-item>

        <el-form-item label="单价" prop="unitPrice">
          <el-input-number
            v-model="productForm.unitPrice"
            :min="0"
            :precision="2"
            placeholder="请输入单价"
          />
          <div class="form-tip">设置为0表示不支持现金购买</div>
        </el-form-item>

        <el-form-item label="所需积分" prop="pointsRequired">
          <el-input-number
            v-model="productForm.pointsRequired"
            :min="0"
            placeholder="请输入所需积分"
          />
          <div class="form-tip">设置为0表示不支持积分兑换</div>
        </el-form-item>

        <el-form-item label="库存数量" prop="stockQuantity">
          <el-input-number
            v-model="productForm.stockQuantity"
            :min="0"
            placeholder="请输入库存数量"
          />
        </el-form-item>

        <el-form-item label="支付方式" prop="paymentTypes">
          <el-checkbox-group v-model="productForm.paymentTypes">
            <el-checkbox label="points">积分</el-checkbox>
            <el-checkbox label="cash">现金</el-checkbox>
            <el-checkbox label="points_cash">积分+现金</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSave" :loading="productsStore.loading">
            保存
          </el-button>
          <el-button @click="$router.back()">
            取消
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card v-else>
      <el-empty description="产品不存在" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { useProductsStore } from '@/stores'

const route = useRoute()
const router = useRouter()
const productsStore = useProductsStore()

const productFormRef = ref<FormInstance>()
const productId = route.params.id as string

const product = computed(() => productsStore.getProductById(productId))

const productForm = reactive({
  name: '',
  description: '',
  photo: '',
  unitPrice: 0,
  pointsRequired: 0,
  stockQuantity: 0,
  paymentTypes: [] as ('points' | 'cash' | 'points_cash')[]
})

const productRules: FormRules = {
  name: [
    { required: true, message: '请输入产品名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入产品描述', trigger: 'blur' }
  ],
  photo: [
    { required: true, message: '请输入图片URL', trigger: 'blur' }
  ],
  unitPrice: [
    { required: true, message: '请输入单价', trigger: 'blur' }
  ],
  pointsRequired: [
    { required: true, message: '请输入所需积分', trigger: 'blur' }
  ],
  stockQuantity: [
    { required: true, message: '请输入库存数量', trigger: 'blur' }
  ],
  paymentTypes: [
    {
      type: 'array',
      required: true,
      message: '请选择至少一种支付方式',
      trigger: 'change',
      min: 1
    }
  ]
}

const initializeForm = () => {
  if (product.value) {
    Object.assign(productForm, {
      name: product.value.name,
      description: product.value.description,
      photo: product.value.photo,
      unitPrice: product.value.unitPrice,
      pointsRequired: product.value.pointsRequired,
      stockQuantity: product.value.stockQuantity,
      paymentTypes: [...product.value.paymentTypes]
    })
  }
}

const handleSave = async () => {
  if (!productFormRef.value) return

  await productFormRef.value.validate(async (valid) => {
    if (valid) {
      // Validate payment types logic
      if (productForm.unitPrice === 0 && productForm.paymentTypes.includes('cash')) {
        ElMessage.error('单价为0时不能选择现金支付')
        return
      }

      if (productForm.pointsRequired === 0 && productForm.paymentTypes.includes('points')) {
        ElMessage.error('积分为0时不能选择积分支付')
        return
      }

      if ((productForm.unitPrice === 0 || productForm.pointsRequired === 0) &&
          productForm.paymentTypes.includes('points_cash')) {
        ElMessage.error('积分+现金支付需要同时设置单价和积分')
        return
      }

      const result = await productsStore.updateProduct(productId, productForm)

      if (result.success) {
        ElMessage.success('产品更新成功')
        router.push('/products')
      } else {
        ElMessage.error(result.message || '更新产品失败')
      }
    }
  })
}

onMounted(() => {
  initializeForm()
})
</script>

<style scoped>
.edit-product {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 18px;
  font-weight: 600;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}
</style>
