<template>
  <h1>Dashboard</h1>
  <el-button type="primary" @click="$router.push('/login')">
    Go to Login
  </el-button>

  <!-- Element Plus table instead of Vuetify -->
  <el-table :data="tableData" stripe style="width: 100%; margin-top: 16px">
    <el-table-column prop="month" label="Month" />
    <el-table-column prop="sales" label="Sales" />
  </el-table>
</template>

<script setup lang="ts">
const tableData = [
  { month: 'Jan', sales: 30 },
  { month: 'Feb', sales: 40 },
  { month: 'Mar', sales: 35 },
  { month: 'Apr', sales: 50 },
  { month: 'May', sales: 49 },
]
</script>