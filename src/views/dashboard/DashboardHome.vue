<template>
  <div class="dashboard-home">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon user-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ usersStore.users.length }}</h3>
              <p>总用户数</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon product-icon">
              <el-icon><Goods /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ productsStore.products.length }}</h3>
              <p>产品总数</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon order-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ ordersStore.orders.length }}</h3>
              <p>订单总数</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon logistics-icon">
              <el-icon><Van /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ logisticsStore.logistics.length }}</h3>
              <p>物流记录</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>最近订单</span>
          </template>
          <el-table :data="recentOrders" style="width: 100%">
            <el-table-column prop="id" label="订单号" width="100" />
            <el-table-column prop="customerName" label="客户" width="120" />
            <el-table-column prop="totalPrice" label="金额" width="100">
              <template #default="scope">
                ¥{{ scope.row.totalPrice }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>系统概览</span>
          </template>
          <div class="system-overview">
            <div class="overview-item">
              <span class="label">待发货订单:</span>
              <span class="value">{{ pendingOrdersCount }}</span>
            </div>
            <div class="overview-item">
              <span class="label">已发货订单:</span>
              <span class="value">{{ shippedOrdersCount }}</span>
            </div>
            <div class="overview-item">
              <span class="label">管理员用户:</span>
              <span class="value">{{ adminUsersCount }}</span>
            </div>
            <div class="overview-item">
              <span class="label">普通用户:</span>
              <span class="value">{{ regularUsersCount }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { User, Goods, Document, Van } from '@element-plus/icons-vue'
import { useUsersStore, useProductsStore, useOrdersStore, useLogisticsStore } from '@/stores'

const usersStore = useUsersStore()
const productsStore = useProductsStore()
const ordersStore = useOrdersStore()
const logisticsStore = useLogisticsStore()

const recentOrders = computed(() => {
  return ordersStore.orders.slice(0, 5)
})

const pendingOrdersCount = computed(() => {
  return ordersStore.orders.filter(order => order.status === 'pending').length
})

const shippedOrdersCount = computed(() => {
  return ordersStore.orders.filter(order => order.status === 'shipped').length
})

const adminUsersCount = computed(() => {
  return usersStore.users.filter(user => user.role === 'admin').length
})

const regularUsersCount = computed(() => {
  return usersStore.users.filter(user => user.role === 'user').length
})

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'warning',
    shipped: 'success',
    cancelled: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    pending: '待发货',
    shipped: '已发货',
    cancelled: '已取消'
  }
  return textMap[status] || status
}
</script>

<style scoped>
.dashboard-home {
  padding: 0;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.user-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.product-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.order-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.logistics-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info h3 {
  margin: 0 0 5px 0;
  font-size: 28px;
  font-weight: bold;
  color: #333;
}

.stat-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.system-overview {
  padding: 10px 0;
}

.overview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.overview-item:last-child {
  border-bottom: none;
}

.overview-item .label {
  color: #666;
  font-size: 14px;
}

.overview-item .value {
  color: #333;
  font-weight: bold;
  font-size: 16px;
}
</style>
