<template>
  <div class="user-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>用户管理</span>
          <el-button type="primary" @click="showAddUserDialog = true">
            <el-icon><Plus /></el-icon>
            Joint (+)
          </el-button>
        </div>
      </template>

      <div class="table-actions">
        <el-button
          type="danger"
          :disabled="selectedUsers.length === 0"
          @click="handleDeleteUsers"
        >
          删除选中用户
        </el-button>
      </div>

      <el-table
        :data="usersStore.users"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        v-loading="usersStore.loading"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="fullName" label="姓名" width="120" />
        <el-table-column prop="phone" label="电话" width="140" />
        <el-table-column prop="address" label="地址" min-width="200" />
        <el-table-column prop="role" label="角色" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.role === 'admin' ? 'success' : 'info'">
              {{ scope.row.role === 'admin' ? '管理员' : '用户' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="120" />
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button
              size="small"
              @click="handleEditRole(scope.row)"
            >
              Role
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- Add User Dialog -->
    <el-dialog
      v-model="showAddUserDialog"
      title="添加新用户"
      width="500px"
      @close="resetAddUserForm"
    >
      <el-form
        ref="addUserFormRef"
        :model="addUserForm"
        :rules="addUserRules"
        label-width="100px"
      >
        <el-form-item label="姓名" prop="fullName">
          <el-input v-model="addUserForm.fullName" placeholder="请输入姓名" />
        </el-form-item>

        <el-form-item label="密码" prop="passcode">
          <el-input
            v-model="addUserForm.passcode"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>

        <el-form-item label="电话" prop="phone">
          <el-input v-model="addUserForm.phone" placeholder="请输入电话号码" />
        </el-form-item>

        <el-form-item label="地址" prop="address">
          <el-input
            v-model="addUserForm.address"
            type="textarea"
            placeholder="请输入地址"
            :rows="3"
          />
        </el-form-item>

        <el-form-item label="角色" prop="role">
          <el-select v-model="addUserForm.role" placeholder="请选择角色">
            <el-option label="用户" value="user" />
            <el-option label="管理员" value="admin" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddUserDialog = false">取消</el-button>
          <el-button type="primary" @click="handleAddUser" :loading="usersStore.loading">
            添加
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- Edit Role Dialog -->
    <el-dialog
      v-model="showEditRoleDialog"
      title="修改用户角色"
      width="400px"
    >
      <div v-if="currentEditUser">
        <p>用户: {{ currentEditUser.fullName }}</p>
        <p>当前角色: {{ currentEditUser.role === 'admin' ? '管理员' : '用户' }}</p>

        <el-form label-width="80px">
          <el-form-item label="新角色">
            <el-select v-model="newRole" placeholder="请选择新角色">
              <el-option label="用户" value="user" />
              <el-option label="管理员" value="admin" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditRoleDialog = false">取消</el-button>
          <el-button type="primary" @click="handleUpdateRole" :loading="usersStore.loading">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { useUsersStore, type User } from '@/stores'

const usersStore = useUsersStore()

// Dialog states
const showAddUserDialog = ref(false)
const showEditRoleDialog = ref(false)

// Form refs
const addUserFormRef = ref<FormInstance>()

// Selected users for deletion
const selectedUsers = ref<User[]>([])

// Add user form
const addUserForm = reactive({
  fullName: '',
  passcode: '',
  phone: '',
  address: '',
  role: 'user' as 'user' | 'admin'
})

const addUserRules: FormRules = {
  fullName: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  passcode: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入电话号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入地址', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

// Edit role
const currentEditUser = ref<User | null>(null)
const newRole = ref<'user' | 'admin'>('user')

// Handle selection change
const handleSelectionChange = (selection: User[]) => {
  selectedUsers.value = selection
}

// Handle add user
const handleAddUser = async () => {
  if (!addUserFormRef.value) return

  await addUserFormRef.value.validate(async (valid) => {
    if (valid) {
      const result = await usersStore.addUser(addUserForm)

      if (result.success) {
        ElMessage.success('用户添加成功')
        showAddUserDialog.value = false
        resetAddUserForm()
      } else {
        ElMessage.error(result.message || '添加用户失败')
      }
    }
  })
}

// Reset add user form
const resetAddUserForm = () => {
  Object.assign(addUserForm, {
    fullName: '',
    passcode: '',
    phone: '',
    address: '',
    role: 'user'
  })
  addUserFormRef.value?.clearValidate()
}

// Handle edit role
const handleEditRole = (user: User) => {
  currentEditUser.value = user
  newRole.value = user.role
  showEditRoleDialog.value = true
}

// Handle update role
const handleUpdateRole = async () => {
  if (!currentEditUser.value) return

  const result = await usersStore.updateUserRole(currentEditUser.value.id, newRole.value)

  if (result.success) {
    ElMessage.success('角色更新成功')
    showEditRoleDialog.value = false
    currentEditUser.value = null
  } else {
    ElMessage.error(result.message || '角色更新失败')
  }
}

// Handle delete users
const handleDeleteUsers = async () => {
  if (selectedUsers.value.length === 0) return

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedUsers.value.length} 个用户吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const userIds = selectedUsers.value.map(user => user.id)
    const result = await usersStore.deleteUsers(userIds)

    if (result.success) {
      ElMessage.success('用户删除成功')
      selectedUsers.value = []
    } else {
      ElMessage.error(result.message || '删除用户失败')
    }
  } catch {
    // User cancelled
  }
}
</script>

<style scoped>
.user-management {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 18px;
  font-weight: 600;
}

.table-actions {
  margin-bottom: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
