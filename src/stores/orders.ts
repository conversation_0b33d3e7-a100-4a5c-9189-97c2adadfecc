import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface OrderItem {
  productId: string
  productName: string
  quantity: number
  unitPrice: number
  pointsUsed: number
}

export interface Order {
  id: string
  customerId: string
  customerName: string
  customerPhone: string
  shippingAddress: string
  items: OrderItem[]
  totalPrice: number
  totalPoints: number
  status: 'pending' | 'shipped' | 'cancelled'
  logisticsNumber?: string
  createdAt: string
  shippedAt?: string
}

export const useOrdersStore = defineStore('orders', () => {
  const orders = ref<Order[]>([
    {
      id: '1',
      customerId: '1',
      customerName: '张三',
      customerPhone: '13800138001',
      shippingAddress: '北京市朝阳区某某街道123号',
      items: [
        {
          productId: '1',
          productName: '高级会员卡',
          quantity: 1,
          unitPrice: 299,
          pointsUsed: 0
        }
      ],
      totalPrice: 299,
      totalPoints: 0,
      status: 'pending',
      createdAt: '2024-01-18'
    },
    {
      id: '2',
      customerId: '2',
      customerName: '李四',
      customerPhone: '13800138002',
      shippingAddress: '上海市浦东新区某某路456号',
      items: [
        {
          productId: '2',
          productName: '积分商品A',
          quantity: 2,
          unitPrice: 0,
          pointsUsed: 500
        }
      ],
      totalPrice: 0,
      totalPoints: 1000,
      status: 'shipped',
      logisticsNumber: 'SF1234567890',
      createdAt: '2024-01-17',
      shippedAt: '2024-01-18'
    }
  ])
  
  const loading = ref(false)

  const addLogistics = async (orderId: string, logisticsNumber: string, updateStatus: boolean = true) => {
    loading.value = true
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const orderIndex = orders.value.findIndex(o => o.id === orderId)
      if (orderIndex !== -1) {
        orders.value[orderIndex].logisticsNumber = logisticsNumber
        if (updateStatus) {
          orders.value[orderIndex].status = 'shipped'
          orders.value[orderIndex].shippedAt = new Date().toISOString().split('T')[0]
        }
      }
      
      return { success: true }
    } catch (error) {
      return { success: false, message: '添加物流信息失败' }
    } finally {
      loading.value = false
    }
  }

  const cancelOrder = async (orderId: string) => {
    loading.value = true
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const orderIndex = orders.value.findIndex(o => o.id === orderId)
      if (orderIndex !== -1) {
        orders.value[orderIndex].status = 'cancelled'
      }
      
      return { success: true }
    } catch (error) {
      return { success: false, message: '取消订单失败' }
    } finally {
      loading.value = false
    }
  }

  const getOrderById = (id: string) => {
    return orders.value.find(order => order.id === id)
  }

  const getPendingOrders = () => {
    return orders.value.filter(order => order.status === 'pending')
  }

  const getShippedOrders = () => {
    return orders.value.filter(order => order.status === 'shipped')
  }

  return {
    orders,
    loading,
    addLogistics,
    cancelOrder,
    getOrderById,
    getPendingOrders,
    getShippedOrders
  }
})
