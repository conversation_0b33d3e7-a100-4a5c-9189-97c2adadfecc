import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface User {
  id: string
  fullName: string
  passcode: string
  phone: string
  address: string
  role: 'user' | 'admin'
  createdAt: string
}

export const useUsersStore = defineStore('users', () => {
  const users = ref<User[]>([
    {
      id: '1',
      fullName: '张三',
      passcode: '123456',
      phone: '13800138001',
      address: '北京市朝阳区',
      role: 'user',
      createdAt: '2024-01-15'
    },
    {
      id: '2',
      fullName: '李四',
      passcode: '123456',
      phone: '13800138002',
      address: '上海市浦东新区',
      role: 'admin',
      createdAt: '2024-01-16'
    },
    {
      id: '3',
      fullName: '王五',
      passcode: '123456',
      phone: '13800138003',
      address: '广州市天河区',
      role: 'user',
      createdAt: '2024-01-17'
    }
  ])
  
  const loading = ref(false)

  const addUser = async (userData: Omit<User, 'id' | 'createdAt'>) => {
    loading.value = true
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const newUser: User = {
        ...userData,
        id: Date.now().toString(),
        createdAt: new Date().toISOString().split('T')[0]
      }
      
      users.value.push(newUser)
      return { success: true }
    } catch (error) {
      return { success: false, message: '添加用户失败' }
    } finally {
      loading.value = false
    }
  }

  const updateUserRole = async (userId: string, role: 'user' | 'admin') => {
    loading.value = true
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const userIndex = users.value.findIndex(u => u.id === userId)
      if (userIndex !== -1) {
        users.value[userIndex].role = role
      }
      
      return { success: true }
    } catch (error) {
      return { success: false, message: '更新用户角色失败' }
    } finally {
      loading.value = false
    }
  }

  const deleteUsers = async (userIds: string[]) => {
    loading.value = true
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      users.value = users.value.filter(user => !userIds.includes(user.id))
      return { success: true }
    } catch (error) {
      return { success: false, message: '删除用户失败' }
    } finally {
      loading.value = false
    }
  }

  return {
    users,
    loading,
    addUser,
    updateUserRole,
    deleteUsers
  }
})
