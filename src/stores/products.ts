import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface Product {
  id: string
  name: string
  description: string
  photo: string
  unitPrice: number
  pointsRequired: number
  stockQuantity: number
  paymentTypes: ('points' | 'cash' | 'points_cash')[]
  createdAt: string
}

export const useProductsStore = defineStore('products', () => {
  const products = ref<Product[]>([
    {
      id: '1',
      name: '高级会员卡',
      description: '享受更多优惠和特权',
      photo: '/images/membership-card.jpg',
      unitPrice: 299,
      pointsRequired: 2000,
      stockQuantity: 100,
      paymentTypes: ['points', 'cash', 'points_cash'],
      createdAt: '2024-01-15'
    },
    {
      id: '2',
      name: '积分商品A',
      description: '热门积分兑换商品',
      photo: '/images/product-a.jpg',
      unitPrice: 0,
      pointsRequired: 500,
      stockQuantity: 50,
      paymentTypes: ['points'],
      createdAt: '2024-01-16'
    },
    {
      id: '3',
      name: '现金商品B',
      description: '现金购买商品',
      photo: '/images/product-b.jpg',
      unitPrice: 199,
      pointsRequired: 0,
      stockQuantity: 75,
      paymentTypes: ['cash'],
      createdAt: '2024-01-17'
    }
  ])
  
  const loading = ref(false)

  const addProduct = async (productData: Omit<Product, 'id' | 'createdAt'>) => {
    loading.value = true
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const newProduct: Product = {
        ...productData,
        id: Date.now().toString(),
        createdAt: new Date().toISOString().split('T')[0]
      }
      
      products.value.push(newProduct)
      return { success: true }
    } catch (error) {
      return { success: false, message: '添加产品失败' }
    } finally {
      loading.value = false
    }
  }

  const updateProduct = async (productId: string, productData: Partial<Product>) => {
    loading.value = true
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const productIndex = products.value.findIndex(p => p.id === productId)
      if (productIndex !== -1) {
        products.value[productIndex] = { ...products.value[productIndex], ...productData }
      }
      
      return { success: true }
    } catch (error) {
      return { success: false, message: '更新产品失败' }
    } finally {
      loading.value = false
    }
  }

  const getProductById = (id: string) => {
    return products.value.find(product => product.id === id)
  }

  return {
    products,
    loading,
    addProduct,
    updateProduct,
    getProductById
  }
})
