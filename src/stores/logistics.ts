import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface LogisticsRemark {
  id: string
  content: string
  createdAt: string
  createdBy: string
}

export interface LogisticsRecord {
  id: string
  orderId: string
  logisticsNumber: string
  creator: string
  status: string
  remarks: LogisticsRemark[]
  createdAt: string
  updatedAt: string
}

export const useLogisticsStore = defineStore('logistics', () => {
  const logistics = ref<LogisticsRecord[]>([
    {
      id: '1',
      orderId: '2',
      logisticsNumber: 'SF1234567890',
      creator: 'Administrator',
      status: '已发货',
      remarks: [
        {
          id: '1',
          content: '包裹已从仓库发出',
          createdAt: '2024-01-18 10:00:00',
          createdBy: 'Administrator'
        },
        {
          id: '2',
          content: '包裹正在运输中',
          createdAt: '2024-01-18 14:30:00',
          createdBy: 'Administrator'
        }
      ],
      createdAt: '2024-01-18',
      updatedAt: '2024-01-18'
    }
  ])
  
  const loading = ref(false)

  const addRemark = async (logisticsId: string, content: string) => {
    loading.value = true
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const logisticsIndex = logistics.value.findIndex(l => l.id === logisticsId)
      if (logisticsIndex !== -1) {
        const newRemark: LogisticsRemark = {
          id: Date.now().toString(),
          content,
          createdAt: new Date().toLocaleString('zh-CN'),
          createdBy: 'Administrator'
        }
        
        logistics.value[logisticsIndex].remarks.push(newRemark)
        logistics.value[logisticsIndex].updatedAt = new Date().toISOString().split('T')[0]
      }
      
      return { success: true }
    } catch (error) {
      return { success: false, message: '添加备注失败' }
    } finally {
      loading.value = false
    }
  }

  const createLogisticsRecord = async (orderId: string, logisticsNumber: string) => {
    loading.value = true
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const newRecord: LogisticsRecord = {
        id: Date.now().toString(),
        orderId,
        logisticsNumber,
        creator: 'Administrator',
        status: '已发货',
        remarks: [],
        createdAt: new Date().toISOString().split('T')[0],
        updatedAt: new Date().toISOString().split('T')[0]
      }
      
      logistics.value.push(newRecord)
      return { success: true, record: newRecord }
    } catch (error) {
      return { success: false, message: '创建物流记录失败' }
    } finally {
      loading.value = false
    }
  }

  const getLogisticsById = (id: string) => {
    return logistics.value.find(record => record.id === id)
  }

  const getLogisticsByOrderId = (orderId: string) => {
    return logistics.value.find(record => record.orderId === orderId)
  }

  return {
    logistics,
    loading,
    addRemark,
    createLogisticsRecord,
    getLogisticsById,
    getLogisticsByOrderId
  }
})
