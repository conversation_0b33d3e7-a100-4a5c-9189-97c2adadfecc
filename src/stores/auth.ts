import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface User {
  id: string
  accountNumber: string
  name: string
  role: 'admin' | 'user'
}

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const isAuthenticated = ref(false)
  const loading = ref(false)

  const login = async (accountNumber: string, password: string) => {
    loading.value = true
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock authentication - in real app, this would be an API call
      if (accountNumber === 'admin' && password === 'password') {
        const userData: User = {
          id: '1',
          accountNumber: 'admin',
          name: 'Administrator',
          role: 'admin'
        }
        
        user.value = userData
        isAuthenticated.value = true
        localStorage.setItem('isAuthenticated', 'true')
        localStorage.setItem('user', JSON.stringify(userData))
        
        return { success: true }
      } else {
        return { success: false, message: '账号或密码错误' }
      }
    } catch (error) {
      return { success: false, message: '登录失败，请重试' }
    } finally {
      loading.value = false
    }
  }

  const logout = () => {
    user.value = null
    isAuthenticated.value = false
    localStorage.removeItem('isAuthenticated')
    localStorage.removeItem('user')
  }

  const initializeAuth = () => {
    const storedAuth = localStorage.getItem('isAuthenticated')
    const storedUser = localStorage.getItem('user')
    
    if (storedAuth === 'true' && storedUser) {
      isAuthenticated.value = true
      user.value = JSON.parse(storedUser)
    }
  }

  return {
    user,
    isAuthenticated,
    loading,
    login,
    logout,
    initializeAuth
  }
})
