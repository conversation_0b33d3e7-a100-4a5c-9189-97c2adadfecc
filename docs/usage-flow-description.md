Based on the designs provided, here is the expected UI flow for the **Membership Service Management System**.

This system is a backend dashboard for administrators to manage users, products, orders, and logistics.

### **1. Authentication**
*   **Login:** The user starts at the "Welcome" screen, which is the login portal for the backend system. They must enter their account number and password to sign in.

### **2. User Management**
This flow allows administrators to manage user accounts.
1.  **Navigate:** From the main sidebar, the administrator clicks on **用户管理 (User Management)**.
2.  **View Users:** They are presented with a list of all registered users, showing details like ID, name, and creation date.
3.  **Add a New User:**
    *   The admin clicks the **Joint (+)** button.
    *   A pop-up form appears, prompting for the new user's Full Name, Passcode, Phone, Address, and Role (User or Administrator).
    *   Clicking **Add** creates the new user account.
4.  **Manage User Roles:**
    *   In the user list, the admin clicks the **Role** button for a specific user.
    *   A pop-up appears, allowing the admin to change the user's role between "User" and "Administrator".
    *   Clicking **Save** applies the change.
5.  **Delete Users:** The admin can delete one or more users by selecting them from the list and clicking the **Delete** button.

### **3. Product Management**
This flow is for managing the products available in the system.
1.  **Navigate:** From the sidebar, the admin selects **产品管理 (Product Management)**.
2.  **View Products:** This screen displays a list of all products with their name, description, stock quantity, and payment type.
3.  **Add a New Product:**
    *   The admin clicks the **添加新产品 (Add New Product)** button.
    *   A new page opens with a form to enter the Product Name, Description, Photo, Unit Price, Points required for exchange, Stock Quantity, and available Payment Types (Points, Cash, or Points + Cash).
    *   Clicking **Save** adds the product to the system.
4.  **Modify a Product:**
    *   From the product list, the admin clicks **修改 (Modify)** on an existing product.
    *   They are taken to a similar form, pre-filled with the product's current information, where they can make changes and save them.

### **4. Order Management and Fulfillment**
This is the core flow for processing customer orders.
1.  **Navigate:** The admin clicks on **订单管理 (Order Management)** in the sidebar.
2.  **View Orders:** The page shows two tabs: **未发货 (Pending Delivery)** and **已发货 (Shipped)**. The admin can see a list of orders with their status and total price.
3.  **Process a New Order:**
    *   The admin selects an order from the **未发货 (Pending Delivery)** tab.
    *   They can click **订单详情 (Order Details)** to view the items purchased and the customer's shipping address.
    *   To ship the item, they click **添加物流 (Add Logistics)**.
4.  **Add Logistics Information:**
    *   On the "Add Logistics" page, the admin enters the **物流单号 (Logistics Number)**.
    *   They can check a box to automatically update the order's status to "Shipped".
    *   After saving, the order is moved to the **已发货 (Shipped)** tab.
5.  **Cancel an Order:** The admin has the option to **取消订单 (Cancel Order)** for orders in either the pending or shipped state.

### **5. Logistics Tracking**
This flow provides a centralized view of all shipments and allows for internal notes.
1.  **Navigate:** The admin clicks on **物流管理 (Logistics Management)** from the sidebar.
2.  **View All Shipments:** This screen lists all shipments, showing the associated order number, logistics number, and creator.
3.  **Add/View Remarks:**
    *   To add an internal note, the admin clicks **添加备注 (Add Remark)**. A new page opens where they can type and save a note.
    *   To see the history of remarks and updates for a shipment, the admin clicks **详情 (Details)**. This page displays a timeline of all notes and status changes for that shipment.